[gd_scene load_steps=2 format=3 uid="uid://ceaoqapkk7l14"]

[ext_resource type="Script" uid="uid://cgbegbst58l8o" path="res://src/PongGame.gd" id="1_hagvt"]

[node name="PongGame" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_hagvt")

[node name="GameArea" type="Control" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -400.0
offset_top = -300.0
offset_right = 400.0
offset_bottom = 300.0
grow_horizontal = 2
grow_vertical = 2

[node name="Background" type="ColorRect" parent="GameArea"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.1, 0.1, 0.1, 1)

[node name="CenterLine" type="ColorRect" parent="GameArea"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -2.0
offset_top = -300.0
offset_right = 2.0
offset_bottom = 300.0
grow_horizontal = 2
grow_vertical = 2
color = Color(1, 1, 1, 0.5)

[node name="LeftPaddle" type="ColorRect" parent="GameArea"]
layout_mode = 1
anchors_preset = 4
anchor_top = 0.5
anchor_bottom = 0.5
offset_left = 20.0
offset_top = -50.0
offset_right = 40.0
offset_bottom = 50.0
grow_vertical = 2

[node name="RightPaddle" type="ColorRect" parent="GameArea"]
layout_mode = 1
anchors_preset = 6
anchor_left = 1.0
anchor_top = 0.5
anchor_right = 1.0
anchor_bottom = 0.5
offset_left = -40.0
offset_top = -50.0
offset_right = -20.0
offset_bottom = 50.0
grow_horizontal = 0
grow_vertical = 2

[node name="Ball" type="ColorRect" parent="GameArea"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -10.0
offset_top = -10.0
offset_right = 10.0
offset_bottom = 10.0
grow_horizontal = 2
grow_vertical = 2

[node name="UI" type="Control" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="LeftScore" type="Label" parent="UI"]
layout_mode = 1
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_left = 50.0
offset_top = -100.0
offset_right = 150.0
offset_bottom = -50.0
grow_vertical = 0
theme_override_font_sizes/font_size = 48
text = "0"
horizontal_alignment = 1
vertical_alignment = 1

[node name="RightScore" type="Label" parent="UI"]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -150.0
offset_top = -100.0
offset_right = -50.0
offset_bottom = -50.0
grow_horizontal = 0
grow_vertical = 0
theme_override_font_sizes/font_size = 48
text = "0"
horizontal_alignment = 1
vertical_alignment = 1

[node name="StatusLabel" type="Label" parent="UI"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -200.0
offset_top = 50.0
offset_right = 200.0
offset_bottom = 100.0
grow_horizontal = 2
theme_override_font_sizes/font_size = 24
text = "Connecting..."
horizontal_alignment = 1
vertical_alignment = 1

[node name="BackButton" type="Button" parent="UI"]
layout_mode = 1
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_left = 50.0
offset_top = -50.0
offset_right = 150.0
offset_bottom = -10.0
grow_vertical = 0
text = "Back"
