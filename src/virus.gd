class_name Virus
extends CharacterBody2D

@export var combat_component: CombatComponent
@export var speed: float = 60.0
@export var scaler_component: MassBasedScalerComponent
@export var mass: float = 10.0:
	set(value):
		mass = value
		if scaler_component:
			scaler_component.update_scale(mass)

var target: Node2D = null

func _ready() -> void:
	add_to_group("virus")
	set_collision_layer_value(4, true)

	scaler_component.initialize(self, self.mass)
	scaler_component.update_scale(self.mass)

	combat_component.combat_owner = self

func _physics_process(delta: float) -> void:
	if mass <= 1: queue_free()

	if not is_instance_valid(target): find_new_target()

	if target:
		velocity = global_position.direction_to(target.global_position) * speed * delta

	combat_component.process_combat(delta)

	move_and_slide()

func find_new_target() -> void:
	var p1_cells: Array[Node] = get_tree().get_nodes_in_group("player1")
	var p2_cells: Array[Node] = get_tree().get_nodes_in_group("player2")
	var all_cells: Array[Node] = p1_cells + p2_cells

	var closest: Node2D = null
	var min_dist_sq: float = INF
	for cell: Node2D in all_cells:
		var dist_sq: float = global_position.distance_squared_to(cell.global_position)
		if dist_sq < min_dist_sq:
			min_dist_sq = dist_sq
			closest = cell
	target = closest
