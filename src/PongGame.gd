class_name PongGame
extends Control

@onready var left_paddle: Area2D = $GameArea/LeftPaddle
@onready var right_paddle: Area2D = $GameArea/RightPaddle
@onready var ball: Area2D = $GameArea/Ball
@onready var left_score_label: Label = $UI/LeftScore
@onready var right_score_label: Label = $UI/RightScore
@onready var status_label: Label = $UI/StatusLabel
@onready var back_button: Button = $UI/BackButton

var game_client: WebSocketPeer
var player_id: int
var player_side: String

func _ready() -> void:
	back_button.pressed.connect(_on_back_button_pressed)

func initialize(websocket: WebSocketPeer, id: int, side: String) -> void:
	game_client = websocket
	player_id = id
	player_side = side
	status_label.text = "Game started! Controls: W/S or arrow keys"

func _process(_delta: float) -> void:
	if not game_client:
		return

	game_client.poll()

	var state: int = game_client.get_ready_state()
	if state == WebSocketPeer.STATE_OPEN:
		_send_player_input()
		_handle_websocket_messages()
	elif state == WebSocketPeer.STATE_CLOSED:
		var code: int = game_client.get_close_code()
		var reason: String = game_client.get_close_reason()
		status_label.text = "Disconnected from server. Code: %d, Reason: %s" % [code, reason]

func _handle_websocket_messages() -> void:
	while game_client.get_available_packet_count() > 0:
		var packet := game_client.get_packet()
		var message := packet.get_string_from_utf8()
		var json: Variant = JSON.parse_string(message)

		if json is Dictionary:
			var json_dict: Dictionary = json
			if json_dict.type == "gameState":
				_handle_game_state(json_dict.data as Dictionary)

func _handle_game_state(data: Dictionary) -> void:
	if data.has("players"):
		var players_data: Dictionary = data.players
		var player_ids: Array[String] = players_data.keys()

		if player_ids.size() >= 1:
			var left_player_data: Dictionary = players_data[player_ids[0]]
			left_paddle.position.y = left_player_data.y

		if player_ids.size() >= 2:
			var right_player_data: Dictionary = players_data[player_ids[1]]
			right_paddle.position.y = right_player_data.y

	if data.has("ball"):
		var ball_data: Dictionary = data.ball
		ball.position = Vector2(ball_data.x as float, ball_data.y as float)

	if data.has("score"):
		var score_data: Dictionary = data.score
		var player_ids: Array[String] = score_data.keys()

		if player_ids.size() >= 2:
			left_score_label.text = str(score_data[player_ids[0]])
			right_score_label.text = str(score_data[player_ids[1]])

	if data.has("status"):
		match data.status:
			"waiting":
				status_label.text = "Waiting for players..."
			"playing":
				status_label.text = "Game in progress!"
			"finished":
				status_label.text = "Game finished!"

func _send_player_input() -> void:
	if game_client.get_ready_state() != WebSocketPeer.STATE_OPEN:
		return

	var action: String = "stop"
	if Input.is_action_pressed("mp_move_up"):
		action = "up"
	elif Input.is_action_pressed("mp_move_down"):
		action = "down"

	var message: String = JSON.stringify({"type": "input", "action": action})
	game_client.send_text(message)

func _on_back_button_pressed() -> void:
	if game_client:
		game_client.close()
	SceneManager.goto_main_menu()
