extends Control

@export var start_game_button: <PERSON><PERSON>
@export var steam_testing_button: But<PERSON>
@export var exit_button: But<PERSON>
@export var multiplayer_button: Button
@export var fmod_button: Button

func _ready() -> void:
	start_game_button.pressed.connect(_on_start_game_button_pressed)
	steam_testing_button.pressed.connect(_on_steam_testing_button_pressed)
	exit_button.pressed.connect(_on_exit_button_pressed)
	multiplayer_button.pressed.connect(_on_multiplayer_button_pressed)
	fmod_button.pressed.connect(_on_fmod_button_pressed)

func _on_start_game_button_pressed() -> void:
	SceneManager.start_new_game()

func _on_steam_testing_button_pressed() -> void:
	SceneManager.goto_steam_testing()

func _on_exit_button_pressed() -> void:
	SceneManager.quit_game()

func _on_multiplayer_button_pressed() -> void:
	SceneManager.goto_multiplayer_lobby()

func _on_fmod_button_pressed() -> void:
	SceneManager.goto_fmod_sounds()
