class_name EndScreen
extends Control

@export var winner_label: Label
@export var replay_button: Button
@export var main_menu_button: Button

func _ready() -> void:
    replay_button.pressed.connect(_on_replay_button_pressed)
    main_menu_button.pressed.connect(_on_main_menu_button_pressed)

func set_winner(winner_player_id: int) -> void:
    winner_label.text = "PLAYER %d WON!" % winner_player_id

func _on_replay_button_pressed() -> void:
    SceneManager.start_new_game()

func _on_main_menu_button_pressed() -> void:
    SceneManager.goto_main_menu()
