extends Control

@export var pong_game_scene: PackedScene
@export var find_game_button: Button
@export var status_label: Label
@export var back_button: But<PERSON>

const MATCHMAKER_URL = "ws://localhost:8000"
const APP_ID = 436480

var matchmaker_client: WebSocketPeer = WebSocketPeer.new()
var game_client: WebSocketPeer = WebSocketPeer.new()
var player_nodes: Dictionary = {}
var is_connecting_to_matchmaker: bool = false
var is_waiting_for_steam_ticket: bool = false

func _ready() -> void:
	if not Steam.isSteamRunning():
		status_label.text = "Error: Failed to start Steam. Please restart Steam and the game."
		find_game_button.disabled = true
		return

	find_game_button.pressed.connect(_on_find_game_pressed)
	back_button.pressed.connect(_on_back_button_pressed)
	Steam.get_ticket_for_web_api.connect(_on_ticket_for_web_api)

func _process(_delta: float) -> void:
	matchmaker_client.poll()
	game_client.poll()

	var matchmaker_state: int = matchmaker_client.get_ready_state()
	if matchmaker_state == WebSocketPeer.STATE_OPEN:
		if is_waiting_for_steam_ticket:
			pass
		else:
			_handle_matchmaker_messages()
	elif matchmaker_state == WebSocketPeer.STATE_CLOSED and is_connecting_to_matchmaker:
		var code: int = matchmaker_client.get_close_code()
		var reason: String = matchmaker_client.get_close_reason()
		status_label.text = "Disconnected from matchmaker. Code: %d, Reason: %s" % [code, reason]
		find_game_button.disabled = false
		back_button.disabled = false
		is_connecting_to_matchmaker = false

	var game_state: int = game_client.get_ready_state()
	if game_state == WebSocketPeer.STATE_OPEN:
		_handle_game_messages()

func _on_find_game_pressed() -> void:
	status_label.text = "Connecting to matchmaker..."
	find_game_button.disabled = true
	back_button.disabled = true
	is_connecting_to_matchmaker = true

	var error: int = matchmaker_client.connect_to_url(MATCHMAKER_URL)
	if error != OK:
		status_label.text = "Connection error to matchmaker: %s" % error
		find_game_button.disabled = false
		back_button.disabled = false
		is_connecting_to_matchmaker = false
	else:
		is_waiting_for_steam_ticket = true
		Steam.getAuthTicketForWebApi("")

func _on_ticket_for_web_api(auth_ticket: int, result: int, _ticket_size: int, ticket_buffer: PackedByteArray) -> void:
	print("Authentication ticket response: %s, result: %s" % [auth_ticket, result])
	is_waiting_for_steam_ticket = false

	if result == Steam.RESULT_OK:
		status_label.text = "Looking for match..."
		var my_steam_id: int = Steam.getSteamID()

		var hex_ticket: String = ticket_buffer.hex_encode()

		var message: String = JSON.stringify({
			"type": "findMatch",
			"ticket": hex_ticket,
			"steamId": str(my_steam_id)
		})

		if matchmaker_client.get_ready_state() == WebSocketPeer.STATE_OPEN:
			matchmaker_client.send_text(message)
		else:
			status_label.text = "Error: connection to matchmaker not established."
			find_game_button.disabled = false
			back_button.disabled = false
			is_connecting_to_matchmaker = false
	else:
		status_label.text = "Error: failed to get ticket from Steam."
		find_game_button.disabled = false
		back_button.disabled = false
		is_connecting_to_matchmaker = false

func _handle_matchmaker_messages() -> void:
	while matchmaker_client.get_available_packet_count() > 0:
		var packet: PackedByteArray = matchmaker_client.get_packet()
		var message: String = packet.get_string_from_utf8()
		var json: Variant = JSON.parse_string(message)

		if json and json is Dictionary:
			var json_dict: Dictionary = json
			match json.type:
				"queued":
					status_label.text = json_dict.get("message", "In queue...")
				"matchFound":
					status_label.text = "Match found! Connecting to game server..."
					matchmaker_client.close()
					var error: int = game_client.connect_to_url(json["gameServerUrl"] as String)
					if error != OK:
						status_label.text = "Connection error to game server: %s" % error
						find_game_button.disabled = false
						back_button.disabled = false
						is_connecting_to_matchmaker = false
				"error":
					status_label.text = "Error: %s" % json_dict.get("message", "Unknown error")
					find_game_button.disabled = false
					back_button.disabled = false
					is_connecting_to_matchmaker = false
					matchmaker_client.close()

func _handle_game_messages() -> void:
	while game_client.get_available_packet_count() > 0:
		var packet: PackedByteArray = game_client.get_packet()
		var message: String = packet.get_string_from_utf8()
		var json: Variant = JSON.parse_string(message)

		if json and json is Dictionary:
			var json_dict: Dictionary = json
			if json_dict.type == "connected":
				status_label.text = "Match found! Loading game..."
				_start_pong_game(json_dict.playerId as int, json_dict.get("playerSide", "left") as String)


func _start_pong_game(player_id: int, player_side: String) -> void:
	var pong_game: PongGame = pong_game_scene.instantiate()
	get_tree().root.add_child(pong_game)
	pong_game.initialize(game_client, player_id, player_side)
	queue_free()

func _on_back_button_pressed() -> void:
	if matchmaker_client.get_ready_state() == WebSocketPeer.STATE_OPEN:
		matchmaker_client.close()
	if game_client.get_ready_state() == WebSocketPeer.STATE_OPEN:
		game_client.close()
	SceneManager.goto_main_menu()
