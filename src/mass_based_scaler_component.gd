class_name MassBasedScalerComponent
extends Node

var _owner_node: Node2D
var _initial_scale: Vector2
var _base_mass: float

func initialize(owner_node: Node2D, base_mass: float) -> void:
    self._owner_node = owner_node
    self._initial_scale = owner_node.scale
    self._base_mass = max(base_mass, 0.001)

func update_scale(current_mass: float) -> void:
    if not is_instance_valid(_owner_node):
        return

    var mass_factor: float = sqrt(current_mass / _base_mass)
    var clamped_factor: float = min(mass_factor, GameSettings.max_scale_from_mass)
    _owner_node.scale = _initial_scale * clamped_factor
