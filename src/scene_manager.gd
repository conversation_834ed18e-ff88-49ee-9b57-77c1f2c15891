extends Node

@export var game_scene: PackedScene
@export var main_menu_scene: PackedScene
@export var end_screen_scene: PackedScene
@export var steam_testing_scene: PackedScene
@export var multiplayer_lobby_scene: PackedScene
@export var fmod_sounds_scene: PackedScene

var current_scene: Node = null

func _ready() -> void:
	var root: Node = get_tree().root
	current_scene = root.get_child(root.get_child_count() - 1)

func goto_scene(packed_scene: PackedScene) -> void:
	if is_instance_valid(current_scene):
		current_scene.queue_free()

	current_scene = packed_scene.instantiate()

	get_tree().root.add_child(current_scene)

func goto_main_menu() -> void:
	goto_scene(main_menu_scene)

func goto_steam_testing() -> void:
	goto_scene(steam_testing_scene)

func goto_multiplayer_lobby() -> void:
	goto_scene(multiplayer_lobby_scene)

func goto_fmod_sounds() -> void:
	goto_scene(fmod_sounds_scene)

func start_new_game() -> void:
	goto_scene(game_scene)
	if current_scene is Game:
		var game: Game = current_scene as Game
		game.game_finished.connect(show_end_screen)

func show_end_screen(winner_player_id: int) -> void:
	goto_scene(end_screen_scene)
	if current_scene is EndScreen:
		var end_screen: EndScreen = current_scene as EndScreen
		end_screen.set_winner(winner_player_id)

func quit_game() -> void:
	get_tree().quit()
